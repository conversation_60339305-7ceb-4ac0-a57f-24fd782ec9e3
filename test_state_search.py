#!/usr/bin/env python3
"""
Test State-Level Property Search
Verify that the modified system works with state-level searches instead of city-level
"""

import asyncio
import json
import os
from src.core.config import Config
from src.services.market_scanner import MarketScanner
from src.services.property_analyzer import PropertyAnalyzer
from src.api.real_estate_client import RealEstateAPIClient

async def test_state_level_search():
    """Test the state-level search functionality"""
    print("🔍 Testing State-Level Property Search")
    print("=" * 50)
    
    try:
        # Initialize configuration
        config = Config()
        print(f"✅ Configuration loaded successfully")
        print(f"   - Total markets: {len(config.get_all_markets())}")
        print(f"   - State-level search enabled: {getattr(config, 'state_level_search_enabled', True)}")
        
        # Display configured markets
        print("\n📍 Configured State Markets:")
        for market in config.get_all_markets()[:5]:  # Show first 5
            print(f"   - {market.state}: {market.priority} priority (radius: {getattr(market, 'search_radius_miles', 75)} miles)")
        
        # Initialize API client
        real_estate_client = RealEstateAPIClient(config)
        property_analyzer = PropertyAnalyzer(config)
        market_scanner = MarketScanner(real_estate_client, property_analyzer, config)
        
        print(f"\n🔧 Services initialized successfully")
        
        # Test a single state search
        test_markets = [m for m in config.get_all_markets() if m.state == "NC"][:1]  # Test NC
        
        if test_markets:
            print(f"\n🎯 Testing search for {test_markets[0].state}")
            print(f"   - Priority: {test_markets[0].priority}")
            print(f"   - Search radius: {getattr(test_markets[0], 'search_radius_miles', 75)} miles")
            
            # Perform test search
            results = await market_scanner.scan_markets(test_markets)
            
            print(f"\n📊 Search Results:")
            print(f"   - Markets scanned: {results.get('markets_scanned', 0)}")
            print(f"   - Total properties found: {results.get('total_properties', 0)}")
            print(f"   - High priority properties: {results.get('high_priority_count', 0)}")
            print(f"   - Scan time: {results.get('total_scan_time', 0):.2f} seconds")
            
            # Show sample properties
            sample_properties = results.get('all_properties', [])[:3]
            if sample_properties:
                print(f"\n🏠 Sample Properties Found:")
                for i, prop in enumerate(sample_properties, 1):
                    print(f"   {i}. {prop.get('address', 'N/A')}")
                    print(f"      Value: ${prop.get('estimatedValue', 0):,}")
                    print(f"      Bedrooms: {prop.get('bedrooms', 'N/A')}")
                    print(f"      State: {prop.get('state', 'N/A')}")
            
            print(f"\n✅ State-level search test completed successfully!")
            
        else:
            print(f"\n❌ No test markets found for NC")
            
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

def test_config_loading():
    """Test configuration loading"""
    print("\n🔧 Testing Configuration Loading")
    print("-" * 30)
    
    try:
        # Load config.json directly
        with open('config.json', 'r') as f:
            config_data = json.load(f)
        
        state_markets = config_data.get("target_markets", {}).get("state_level_searches", [])
        print(f"✅ Found {len(state_markets)} state-level markets in config.json")
        
        # Show first few markets
        for market in state_markets[:3]:
            print(f"   - {market['state']}: {market['priority']} priority")
            print(f"     Radius: {market.get('search_radius_miles', 75)} miles")
            print(f"     Focus areas: {', '.join(market.get('focus_areas', [])[:3])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config loading failed: {str(e)}")
        return False

async def main():
    """Main test function"""
    print("STATE-LEVEL SEARCH TESTING")
    print("=" * 50)
    
    # Test 1: Configuration loading
    config_ok = test_config_loading()
    
    if config_ok:
        # Test 2: State-level search functionality
        await test_state_level_search()
    else:
        print("❌ Skipping search test due to config issues")
    
    print("\n" + "=" * 50)
    print("Testing completed!")

if __name__ == "__main__":
    asyncio.run(main())
