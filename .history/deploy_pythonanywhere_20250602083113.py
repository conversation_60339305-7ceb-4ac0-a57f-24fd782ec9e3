#!/usr/bin/env python3
"""
PythonAnywhere Deployment Script for Section 8 Property Monitor
This script sets up the production monitor to run on PythonAnywhere
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def setup_environment():
    """Set up the Python environment and install dependencies"""
    print("🔧 Setting up Python environment...")
    
    # Install required packages
    packages = [
        'asyncio',
        'asyncpg',
        'aiohttp',
        'python-dotenv',
        'resend',
        'schedule'
    ]
    
    for package in packages:
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], check=True)
            print(f"✅ Installed {package}")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")
            return False
    
    return True

def create_task_script():
    """Create the task script for PythonAnywhere scheduled tasks"""
    task_script = """#!/usr/bin/env python3
import sys
import os
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

# Import and run the monitor
from production_monitor import main
import asyncio

if __name__ == "__main__":
    asyncio.run(main())
"""
    
    with open('run_monitor.py', 'w') as f:
        f.write(task_script)
    
    # Make it executable
    os.chmod('run_monitor.py', 0o755)
    print("✅ Created task script: run_monitor.py")

def create_web_app_file():
    """Create WSGI file for PythonAnywhere web app"""
    wsgi_content = """
import sys
import os
from pathlib import Path

# Add your project directory to the Python path
project_home = '/home/<USER>/section8-monitor'  # Update this path
if project_home not in sys.path:
    sys.path.insert(0, project_home)

# Set environment variables
os.environ['DATABASE_URL'] = 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require'
os.environ['RESEND_API_KEY'] = 're_Yp76vKMM_8W3kEDTidZSCJ77Uc3yiyZgP'
os.environ['ALERT_EMAIL'] = '<EMAIL>'

# Import your Flask/FastAPI app
from src.web.dashboard import app as application

# For debugging
if __name__ == "__main__":
    application.run(debug=True)
"""
    
    with open('wsgi.py', 'w') as f:
        f.write(wsgi_content)
    
    print("✅ Created WSGI file: wsgi.py")

def create_deployment_instructions():
    """Create deployment instructions file"""
    instructions = """
# Section 8 Property Monitor - PythonAnywhere Deployment Instructions

## 1. Upload Files
Upload all project files to your PythonAnywhere account:
- production_monitor.py
- config.json
- .env
- requirements.txt
- run_monitor.py
- wsgi.py
- dashboard/ (entire folder)

## 2. Set Up Scheduled Task
1. Go to PythonAnywhere Dashboard > Tasks
2. Create a new scheduled task
3. Set command: python3.10 /home/<USER>/section8-monitor/run_monitor.py
4. Set schedule: Every 1 minute
5. Enable the task

## 3. Set Up Web App (Optional)
1. Go to PythonAnywhere Dashboard > Web
2. Create a new web app
3. Choose Manual configuration
4. Set source code directory: /home/<USER>/section8-monitor
5. Set WSGI configuration file: /home/<USER>/section8-monitor/wsgi.py
6. Reload the web app

## 4. Environment Variables
Make sure these are set in your .env file:
- DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
- RESEND_API_KEY=re_Yp76vKMM_8W3kEDTidZSCJ77Uc3yiyZgP
- ALERT_EMAIL=<EMAIL>
- REAL_ESTATE_API_KEY=AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914
- REAL_ESTATE_API_URL=https://api.realestateapi.com

## 5. Test the Setup
1. Run the monitor manually first: python3.10 production_monitor.py
2. Check logs for any errors
3. Verify database connection
4. Test email notifications

## 6. Monitor Performance
- Check PythonAnywhere logs regularly
- Monitor email alerts
- Verify database is being populated
- Check for any API rate limiting issues

## 7. Troubleshooting
- If tasks fail, check the error logs in PythonAnywhere
- Ensure all dependencies are installed
- Verify database connection string
- Check API keys are valid
- Monitor CPU seconds usage (PythonAnywhere limits)

## 8. Scaling Considerations
- Monitor API call limits
- Adjust search frequency if needed
- Consider upgrading PythonAnywhere plan for more CPU seconds
- Implement error recovery mechanisms
"""
    
    with open('DEPLOYMENT_INSTRUCTIONS.md', 'w') as f:
        f.write(instructions)
    
    print("✅ Created deployment instructions: DEPLOYMENT_INSTRUCTIONS.md")

def create_requirements_file():
    """Create requirements.txt for PythonAnywhere"""
    requirements = """asyncio
asyncpg==0.29.0
aiohttp==3.9.1
python-dotenv==1.0.0
resend==2.10.0
schedule==1.2.0
fastapi==0.104.1
uvicorn==0.24.0
psycopg2-binary==2.9.9
"""
    
    with open('requirements_pythonanywhere.txt', 'w') as f:
        f.write(requirements)
    
    print("✅ Created PythonAnywhere requirements: requirements_pythonanywhere.txt")

def create_test_script():
    """Create a test script to verify the setup"""
    test_script = """#!/usr/bin/env python3
import asyncio
import os
from dotenv import load_dotenv

async def test_setup():
    print("🧪 Testing Section 8 Monitor Setup...")
    
    # Load environment variables
    load_dotenv()
    
    # Test environment variables
    required_vars = [
        'DATABASE_URL',
        'RESEND_API_KEY', 
        'ALERT_EMAIL',
        'REAL_ESTATE_API_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    else:
        print("✅ All environment variables present")
    
    # Test database connection
    try:
        import asyncpg
        conn = await asyncpg.connect(os.getenv('DATABASE_URL'))
        await conn.close()
        print("✅ Database connection successful")
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False
    
    # Test email service
    try:
        import resend
        resend.api_key = os.getenv('RESEND_API_KEY')
        print("✅ Email service configured")
    except Exception as e:
        print(f"❌ Email service configuration failed: {e}")
        return False
    
    print("🎉 Setup test completed successfully!")
    return True

if __name__ == "__main__":
    asyncio.run(test_setup())
"""
    
    with open('test_setup.py', 'w') as f:
        f.write(test_script)
    
    os.chmod('test_setup.py', 0o755)
    print("✅ Created test script: test_setup.py")

def main():
    """Main deployment setup function"""
    print("🚀 Setting up Section 8 Property Monitor for PythonAnywhere...")
    print("=" * 60)
    
    # Create necessary files
    create_task_script()
    create_web_app_file()
    create_deployment_instructions()
    create_requirements_file()
    create_test_script()
    
    print("\n" + "=" * 60)
    print("✅ Deployment setup completed!")
    print("\nNext steps:")
    print("1. Upload all files to your PythonAnywhere account")
    print("2. Follow the instructions in DEPLOYMENT_INSTRUCTIONS.md")
    print("3. Run test_setup.py to verify everything works")
    print("4. Set up the scheduled task to run every minute")
    print("\n📧 You'll receive email alerts at: <EMAIL>")
    print("🗄️  Data will be stored in the Neon PostgreSQL database")
    print("⏰ Monitor will run every 1 minute scanning 20 top cities")

if __name__ == "__main__":
    main()
