#!/usr/bin/env python3
"""
Package Installation Script for Section 8 Monitor
Installs required packages including Resend and Mapbox
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔧 {description}")
    print(f"Running: {command}")
    print("-" * 50)
    
    try:
        result = subprocess.run(
            command.split(),
            capture_output=True,
            text=True,
            check=True
        )
        print("✅ Success!")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def check_package(package_name):
    """Check if a package is installed"""
    try:
        __import__(package_name)
        print(f"✅ {package_name} is installed")
        return True
    except ImportError:
        print(f"❌ {package_name} is not installed")
        return False

def main():
    """Main installation process"""
    
    print("🚀 Section 8 Monitor - Package Installation")
    print("=" * 60)
    
    # Check Python version
    print(f"\n📋 Python Version: {sys.version}")
    print(f"📋 Python Executable: {sys.executable}")
    
    # Priority packages for Section 8 Monitor
    priority_packages = [
        ("resend==2.10.0", "Resend Email Service"),
        ("mapbox==0.10.0", "Mapbox Mapping Service"),
        ("aiohttp", "Async HTTP Client"),
        ("asyncpg", "PostgreSQL Async Driver"),
        ("python-dotenv", "Environment Variables")
    ]
    
    print(f"\n🎯 Installing Priority Packages")
    print("=" * 40)
    
    success_count = 0
    total_count = len(priority_packages)
    
    for package, description in priority_packages:
        if run_command(f"pip install {package}", f"Installing {description}"):
            success_count += 1
    
    print(f"\n📊 Installation Results")
    print("=" * 30)
    print(f"✅ Successful: {success_count}/{total_count}")
    print(f"❌ Failed: {total_count - success_count}/{total_count}")
    
    # Test package imports
    print(f"\n🧪 Testing Package Imports")
    print("=" * 30)
    
    test_packages = ["resend", "mapbox", "aiohttp", "asyncpg", "dotenv"]
    
    for package in test_packages:
        check_package(package)
    
    # Check API keys
    print(f"\n🔑 Checking API Keys")
    print("=" * 20)
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        mapbox_key = os.getenv("MAPBOX_ACCESS_TOKEN")
        resend_key = os.getenv("RESEND_API_KEY")
        
        if mapbox_key:
            print(f"✅ Mapbox API key found: {mapbox_key[:20]}...")
        else:
            print("❌ Mapbox API key not found in .env")
        
        if resend_key:
            print(f"✅ Resend API key found: {resend_key[:20]}...")
        else:
            print("❌ Resend API key not found in .env")
            
    except Exception as e:
        print(f"❌ Error checking API keys: {e}")
    
    # Installation summary
    print(f"\n🎉 Installation Summary")
    print("=" * 30)
    
    if success_count == total_count:
        print("✅ All priority packages installed successfully!")
        print("✅ Section 8 Monitor is ready to run")
        print("\n📋 Next Steps:")
        print("   1. Verify API keys in .env file")
        print("   2. Test the property type mapping")
        print("   3. Run the Section 8 monitor")
    else:
        print("⚠️  Some packages failed to install")
        print("📋 Manual installation may be required:")
        print("   pip install resend==2.10.0")
        print("   pip install mapbox==0.10.0")
        print("   pip install -r requirements.txt")
    
    return success_count == total_count

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Installation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Installation failed with error: {e}")
        sys.exit(1)
