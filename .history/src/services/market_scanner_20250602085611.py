#!/usr/bin/env python3
"""
Market Scanner for Section 8 Investor Pro
Scans multiple markets concurrently for Section 8 investment opportunities
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import time

from ..api.real_estate_client import RealEstateAPIClient
from ..services.property_analyzer import PropertyAnalyzer, PropertyAnalysis
from ..core.config import Config, MarketConfig, InvestmentCriteria
from ..utils.property_type_mapper import property_type_mapper

class MarketScanner:
    """Scans multiple markets for Section 8 investment opportunities"""
    
    def __init__(
        self,
        real_estate_client: RealEstateAPIClient,
        property_analyzer: PropertyAnalyzer,
        config: Config
    ):
        """
        Initialize market scanner
        
        Args:
            real_estate_client: Real Estate API client
            property_analyzer: Property analyzer service
            config: Application configuration
        """
        self.real_estate_client = real_estate_client
        self.property_analyzer = property_analyzer
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Scanning statistics
        self.scan_stats = {
            "total_scans": 0,
            "total_properties_analyzed": 0,
            "total_high_priority": 0,
            "total_api_calls": 0,
            "last_scan_time": None
        }
    
    async def scan_all_markets(self) -> Dict[str, Any]:
        """
        Scan all configured markets for investment opportunities
        
        Returns:
            Dictionary containing scan results and statistics
        """
        self.logger.info("Starting comprehensive market scan...")
        start_time = time.time()
        
        # Get all markets prioritized by weight and priority
        markets = self._prioritize_markets()
        
        # Scan markets with controlled concurrency
        scan_results = await self._scan_markets_concurrent(markets)
        
        # Compile comprehensive results
        total_results = self._compile_scan_results(scan_results, start_time)
        
        # Update scanning statistics
        self._update_scan_stats(total_results)
        
        self.logger.info(
            f"Market scan completed: {total_results['total_properties']} properties analyzed, "
            f"{total_results['high_priority_count']} high priority found"
        )
        
        return total_results
    
    async def scan_markets(self, markets: List[MarketConfig]) -> Dict[str, Any]:
        """
        Scan specific markets for investment opportunities
        
        Args:
            markets: List of markets to scan
        
        Returns:
            Dictionary containing scan results
        """
        self.logger.info(f"Scanning {len(markets)} specified markets...")
        start_time = time.time()
        
        # Scan markets with controlled concurrency
        scan_results = await self._scan_markets_concurrent(markets)
        
        # Compile results
        total_results = self._compile_scan_results(scan_results, start_time)
        
        return total_results
    
    def _prioritize_markets(self) -> List[MarketConfig]:
        """Prioritize markets based on weight and priority"""
        
        all_markets = self.config.get_all_markets()
        
        # Sort by priority (VERY_HIGH > HIGH > MEDIUM > LOW) and weight
        priority_order = {"VERY_HIGH": 4, "HIGH": 3, "MEDIUM": 2, "LOW": 1}
        
        prioritized_markets = sorted(
            all_markets,
            key=lambda m: (priority_order.get(m.priority, 0), m.weight),
            reverse=True
        )
        
        self.logger.debug(f"Prioritized {len(prioritized_markets)} markets for scanning")
        return prioritized_markets
    
    async def _scan_markets_concurrent(
        self, 
        markets: List[MarketConfig]
    ) -> List[Dict[str, Any]]:
        """Scan markets with controlled concurrency"""
        
        # Create semaphore to limit concurrent scans
        semaphore = asyncio.Semaphore(self.config.MAX_CONCURRENT_SEARCHES)
        
        # Create scan tasks
        tasks = [
            self._scan_single_market_with_semaphore(semaphore, market)
            for market in markets
        ]
        
        # Execute tasks and gather results
        scan_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and log errors
        valid_results = []
        for i, result in enumerate(scan_results):
            if isinstance(result, Exception):
                market_name = getattr(markets[i], 'city', markets[i].state) if hasattr(markets[i], 'city') and markets[i].city else markets[i].state
                self.logger.error(f"Market scan failed for {market_name}, {markets[i].state}: {str(result)}")
            else:
                valid_results.append(result)
        
        return valid_results
    
    async def _scan_single_market_with_semaphore(
        self,
        semaphore: asyncio.Semaphore,
        market: MarketConfig
    ) -> Dict[str, Any]:
        """Scan a single market with semaphore control"""
        
        async with semaphore:
            return await self._scan_single_market(market)
    
    async def _scan_single_market(self, market: MarketConfig) -> Dict[str, Any]:
        """
        Scan a single market for investment opportunities
        
        Args:
            market: Market configuration
        
        Returns:
            Dictionary containing market scan results
        """
        market_name = getattr(market, 'city', market.state) if hasattr(market, 'city') and market.city else market.state
        self.logger.info(f"Scanning market: {market_name}, {market.state}")
        market_start_time = time.time()

        market_results = {
            "market": market,
            "properties": [],
            "high_priority_properties": [],
            "medium_priority_properties": [],
            "low_priority_properties": [],
            "total_properties": 0,
            "api_calls": 0,
            "scan_time": 0,
            "errors": []
        }
        
        try:
            # Scan different investment criteria types
            criteria_types = ["primary", "opportunity", "premium"]
            
            for criteria_type in criteria_types:
                self.logger.debug(f"Scanning {market.city} with {criteria_type} criteria")
                
                # Get search results for this criteria type
                search_results = await self._search_market_properties(market, criteria_type)
                market_results["api_calls"] += 1
                
                if not search_results:
                    continue
                
                properties = search_results.get('data', [])
                market_results["total_properties"] += len(properties)
                
                # Analyze each property
                for property_data in properties:
                    try:
                        analysis = await self.property_analyzer.analyze_property(
                            property_data, criteria_type
                        )
                        
                        if analysis and analysis.investment_priority != "SKIP":
                            property_result = {
                                "property_data": property_data,
                                "analysis": analysis,
                                "criteria_type": criteria_type,
                                "market": f"{market.city}, {market.state}"
                            }
                            
                            market_results["properties"].append(property_result)
                            
                            # Categorize by priority
                            if analysis.investment_priority == "HIGH":
                                market_results["high_priority_properties"].append(property_result)
                            elif analysis.investment_priority == "MEDIUM":
                                market_results["medium_priority_properties"].append(property_result)
                            elif analysis.investment_priority == "LOW":
                                market_results["low_priority_properties"].append(property_result)
                    
                    except Exception as e:
                        self.logger.warning(f"Property analysis failed: {str(e)}")
                        market_results["errors"].append(str(e))
                
                # Rate limiting between criteria searches
                await asyncio.sleep(self.config.API_RATE_LIMIT_DELAY)
        
        except Exception as e:
            self.logger.error(f"Market scan failed for {market.city}: {str(e)}")
            market_results["errors"].append(str(e))
        
        # Calculate scan time
        market_results["scan_time"] = time.time() - market_start_time
        
        self.logger.info(
            f"Market scan completed for {market.city}: "
            f"{len(market_results['properties'])} opportunities found "
            f"({len(market_results['high_priority_properties'])} high priority)"
        )
        
        return market_results
    
    async def _search_market_properties(
        self, 
        market: MarketConfig, 
        criteria_type: str
    ) -> Optional[Dict[str, Any]]:
        """
        Search for properties in a market using specific criteria
        
        Args:
            market: Market configuration
            criteria_type: Investment criteria type
        
        Returns:
            Search results from Real Estate API
        """
        try:
            # Get investment criteria
            criteria = self.config.get_investment_criteria(criteria_type)
            
            # Build search parameters
            search_params = self._build_search_parameters(market, criteria)
            
            # Execute search
            results = await self.real_estate_client.search_properties(search_params)
            
            if results:
                market_name = getattr(market, 'city', market.state) if hasattr(market, 'city') and market.city else market.state
                self.logger.debug(
                    f"Found {len(results.get('data', []))} properties in "
                    f"{market_name} with {criteria_type} criteria"
                )
            
            return results
        
        except Exception as e:
            market_name = getattr(market, 'city', market.state) if hasattr(market, 'city') and market.city else market.state
            self.logger.error(
                f"Property search failed for {market_name} ({criteria_type}): {str(e)}"
            )
            return None
    
    def _build_search_parameters(
        self,
        market: MarketConfig,
        criteria: InvestmentCriteria
    ) -> Dict[str, Any]:
        """Build search parameters for Real Estate API using state-level searches"""

        # Normalize property types using the mapper
        property_type_params = property_type_mapper.build_search_parameters(
            criteria.property_types, section8_only=True
        )

        params = {
            # Location parameters - STATE LEVEL SEARCH (no city restriction)
            "state": market.state,

            # Use broader radius for state-level coverage
            "radius": market.search_radius_miles if hasattr(market, 'search_radius_miles') else 75,

            # Price range (use correct API parameter names)
            "estimated_value_min": criteria.min_price,
            "estimated_value_max": criteria.max_price,

            # Property specifications
            "bedrooms_min": criteria.min_bedrooms,
            "bedrooms_max": criteria.max_bedrooms,
            "bathrooms_min": criteria.min_bathrooms,
            "bathrooms_max": criteria.max_bathrooms,
            "square_feet_min": criteria.min_square_feet,
            "square_feet_max": criteria.max_square_feet,
            "year_built_min": criteria.min_year_built,

            # Investment filters
            "absentee_owner": True,  # Focus on investment properties
            "owner_occupied": False,  # Exclude owner-occupied

            # Result parameters
            "limit": self.config.MAX_PROPERTIES_PER_SEARCH,
            "sort": "estimated_value",  # Start with lower-priced properties
            "sort_direction": "asc"
        }

        # Add normalized property type
        params.update(property_type_params)

        # Add optional filters based on market priority
        if market.priority in ["VERY_HIGH", "HIGH"]:
            # More aggressive filtering for high-priority markets
            params.update({
                "vacant": True,  # Focus on vacant properties
                "foreclosure": True  # Include foreclosure opportunities
            })

        return params
    
    def _compile_scan_results(
        self, 
        scan_results: List[Dict[str, Any]], 
        start_time: float
    ) -> Dict[str, Any]:
        """Compile results from all market scans"""
        
        compiled_results = {
            "scan_timestamp": datetime.now().isoformat(),
            "total_scan_time": time.time() - start_time,
            "markets_scanned": len(scan_results),
            "total_properties": 0,
            "total_api_calls": 0,
            "high_priority_count": 0,
            "medium_priority_count": 0,
            "low_priority_count": 0,
            "all_properties": [],
            "high_priority_properties": [],
            "medium_priority_properties": [],
            "low_priority_properties": [],
            "market_summaries": [],
            "errors": []
        }
        
        for market_result in scan_results:
            # Aggregate statistics
            compiled_results["total_properties"] += market_result["total_properties"]
            compiled_results["total_api_calls"] += market_result["api_calls"]
            compiled_results["high_priority_count"] += len(market_result["high_priority_properties"])
            compiled_results["medium_priority_count"] += len(market_result["medium_priority_properties"])
            compiled_results["low_priority_count"] += len(market_result["low_priority_properties"])
            
            # Aggregate properties
            compiled_results["all_properties"].extend(market_result["properties"])
            compiled_results["high_priority_properties"].extend(market_result["high_priority_properties"])
            compiled_results["medium_priority_properties"].extend(market_result["medium_priority_properties"])
            compiled_results["low_priority_properties"].extend(market_result["low_priority_properties"])
            
            # Aggregate errors
            compiled_results["errors"].extend(market_result["errors"])
            
            # Create market summary
            market = market_result["market"]
            market_summary = {
                "city": market.city,
                "state": market.state,
                "priority": market.priority,
                "weight": market.weight,
                "total_properties": market_result["total_properties"],
                "opportunities_found": len(market_result["properties"]),
                "high_priority": len(market_result["high_priority_properties"]),
                "medium_priority": len(market_result["medium_priority_properties"]),
                "low_priority": len(market_result["low_priority_properties"]),
                "scan_time": market_result["scan_time"],
                "api_calls": market_result["api_calls"]
            }
            compiled_results["market_summaries"].append(market_summary)
        
        # Sort properties by investment score
        compiled_results["high_priority_properties"].sort(
            key=lambda x: x["analysis"].investment_score, reverse=True
        )
        compiled_results["medium_priority_properties"].sort(
            key=lambda x: x["analysis"].investment_score, reverse=True
        )
        
        return compiled_results
    
    def _update_scan_stats(self, scan_results: Dict[str, Any]):
        """Update scanning statistics"""
        
        self.scan_stats["total_scans"] += 1
        self.scan_stats["total_properties_analyzed"] += scan_results["total_properties"]
        self.scan_stats["total_high_priority"] += scan_results["high_priority_count"]
        self.scan_stats["total_api_calls"] += scan_results["total_api_calls"]
        self.scan_stats["last_scan_time"] = datetime.now()
    
    async def scan_priority_markets(self, priority: str = "HIGH") -> Dict[str, Any]:
        """
        Scan only markets with specific priority level
        
        Args:
            priority: Priority level to scan (VERY_HIGH, HIGH, MEDIUM, LOW)
        
        Returns:
            Scan results for priority markets
        """
        priority_markets = self.config.get_markets_by_priority(priority)
        
        if not priority_markets:
            self.logger.warning(f"No markets found with priority: {priority}")
            return {
                "markets_scanned": 0,
                "total_properties": 0,
                "high_priority_count": 0,
                "all_properties": []
            }
        
        self.logger.info(f"Scanning {len(priority_markets)} {priority} priority markets")
        return await self.scan_markets(priority_markets)
    
    async def scan_state_markets(self, state: str) -> Dict[str, Any]:
        """
        Scan all markets in a specific state
        
        Args:
            state: State code (e.g., 'MI', 'OH')
        
        Returns:
            Scan results for state markets
        """
        state_markets = [
            market for market in self.config.get_all_markets()
            if market.state.upper() == state.upper()
        ]
        
        if not state_markets:
            self.logger.warning(f"No markets found for state: {state}")
            return {
                "markets_scanned": 0,
                "total_properties": 0,
                "high_priority_count": 0,
                "all_properties": []
            }
        
        self.logger.info(f"Scanning {len(state_markets)} markets in {state}")
        return await self.scan_markets(state_markets)
    
    async def quick_scan(self, market_count: int = 5) -> Dict[str, Any]:
        """
        Perform quick scan of top priority markets
        
        Args:
            market_count: Number of top markets to scan
        
        Returns:
            Quick scan results
        """
        top_markets = self._prioritize_markets()[:market_count]
        
        self.logger.info(f"Performing quick scan of top {market_count} markets")
        return await self.scan_markets(top_markets)
    
    def get_scan_statistics(self) -> Dict[str, Any]:
        """Get current scanning statistics"""
        
        stats = self.scan_stats.copy()
        
        # Add calculated metrics
        if stats["total_scans"] > 0:
            stats["avg_properties_per_scan"] = stats["total_properties_analyzed"] / stats["total_scans"]
            stats["high_priority_rate"] = (stats["total_high_priority"] / max(stats["total_properties_analyzed"], 1)) * 100
        else:
            stats["avg_properties_per_scan"] = 0
            stats["high_priority_rate"] = 0
        
        # Format last scan time
        if stats["last_scan_time"]:
            stats["last_scan_time"] = stats["last_scan_time"].isoformat()
        
        return stats
    
    async def test_market_connectivity(self) -> Dict[str, Any]:
        """Test connectivity to all markets"""
        
        self.logger.info("Testing market connectivity...")
        
        test_results = {
            "total_markets": 0,
            "successful_markets": 0,
            "failed_markets": 0,
            "market_tests": []
        }
        
        # Test a few representative markets
        test_markets = self.config.get_tier_1_markets()[:3]
        test_results["total_markets"] = len(test_markets)
        
        for market in test_markets:
            try:
                # Simple search to test connectivity
                search_params = {
                    "city": market.city,
                    "state": market.state,
                    "limit": 1
                }
                
                result = await self.real_estate_client.search_properties(search_params)
                
                if result is not None:
                    test_results["successful_markets"] += 1
                    status = "SUCCESS"
                else:
                    test_results["failed_markets"] += 1
                    status = "FAILED"
                
                test_results["market_tests"].append({
                    "market": f"{market.city}, {market.state}",
                    "status": status
                })
                
            except Exception as e:
                test_results["failed_markets"] += 1
                test_results["market_tests"].append({
                    "market": f"{market.city}, {market.state}",
                    "status": "ERROR",
                    "error": str(e)
                })
        
        success_rate = (test_results["successful_markets"] / test_results["total_markets"]) * 100
        test_results["success_rate"] = success_rate
        
        self.logger.info(f"Market connectivity test completed: {success_rate:.1f}% success rate")
        
        return test_results
