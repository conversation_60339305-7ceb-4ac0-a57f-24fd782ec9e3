#!/usr/bin/env python3
"""
Configuration Management for Section 8 Investor Pro
Handles all configuration settings, market definitions, and environment variables
"""

import os
import json
from typing import Dict, List, Any, Optional
from pathlib import Path
from dataclasses import dataclass
from dotenv import load_dotenv

@dataclass
class MarketConfig:
    """Configuration for a target market"""
    state: str
    priority: str
    weight: float
    search_radius_miles: int = 75
    expected_fmr_2br: int = 800
    expected_fmr_3br: int = 1000
    focus_areas: List[str] = None
    city: str = None  # Optional for backward compatibility
    metro: str = None  # Optional for backward compatibility
    
@dataclass
class InvestmentCriteria:
    """Investment criteria configuration"""
    min_price: int
    max_price: int
    min_bedrooms: int
    max_bedrooms: int
    min_bathrooms: float
    max_bathrooms: float
    min_square_feet: int
    max_square_feet: int
    min_year_built: int
    property_types: List[str]
    min_roi_percentage: float
    target_roi_percentage: float

class Config:
    """Main configuration class"""
    
    def __init__(self):
        """Initialize configuration from environment and config files"""
        # Load environment variables
        load_dotenv()
        
        # API Configuration
        self.REAL_ESTATE_API_KEY = os.getenv("REAL_ESTATE_API_KEY")
        self.REAL_ESTATE_API_URL = os.getenv("REAL_ESTATE_API_URL", "https://api.realestateapi.com")
        self.HUD_API_KEY = os.getenv("HUD_API_KEY")
        self.HUD_API_URL = os.getenv("HUD_API_URL", "https://www.huduser.gov/hudapi/public")
        
        # Database Configuration
        self.DATABASE_URL = os.getenv("DATABASE_URL")
        self.DIRECT_URL = os.getenv("DIRECT_URL")
        
        # Email Configuration
        self.RESEND_API_KEY = os.getenv("RESEND_API_KEY")
        self.ALERT_EMAIL = os.getenv("ALERT_EMAIL")
        
        # Other APIs
        self.MAPBOX_ACCESS_TOKEN = os.getenv("MAPBOX_ACCESS_TOKEN")
        self.ZILLOW_API_KEY = os.getenv("ZILLOW_API_KEY")
        self.GOOGLE_MAPS_API_KEY = os.getenv("GOOGLE_MAPS_API_KEY")
        self.CENSUS_API_KEY = os.getenv("CENSUS_API_KEY")
        
        # App Configuration
        self.APP_URL = os.getenv("APP_URL", "http://localhost:3000")
        self.APP_NAME = os.getenv("APP_NAME", "Section8 Investor Pro")
        self.NODE_ENV = os.getenv("NODE_ENV", "development")
        
        # Performance Settings
        self.SEARCH_INTERVAL_MINUTES = int(os.getenv("SEARCH_INTERVAL_MINUTES", "5"))
        self.MAX_CONCURRENT_SEARCHES = int(os.getenv("MAX_CONCURRENT_SEARCHES", "10"))
        self.API_RATE_LIMIT_DELAY = int(os.getenv("API_RATE_LIMIT_DELAY", "2"))
        self.MAX_PROPERTIES_PER_SEARCH = int(os.getenv("MAX_PROPERTIES_PER_SEARCH", "100"))
        self.MAX_DAILY_API_CALLS = int(os.getenv("MAX_DAILY_API_CALLS", "10000"))
        
        # Rate Limiting
        self.RATE_LIMIT_WINDOW_MS = int(os.getenv("RATE_LIMIT_WINDOW_MS", "3600000"))
        self.RATE_LIMIT_MAX_REQUESTS = int(os.getenv("RATE_LIMIT_MAX_REQUESTS", "1000"))
        
        # Logging
        self.LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
        
        # Load market configurations
        self._load_market_configs()
        
        # Load investment criteria
        self._load_investment_criteria()
        
        # Validate configuration
        self._validate_config()
    
    def _load_market_configs(self):
        """Load market configurations from config.json"""
        try:
            # Load config.json file
            config_path = Path("config.json")
            if config_path.exists():
                with open(config_path, 'r') as f:
                    config_data = json.load(f)

                # Load state-level markets from config
                state_markets = config_data.get("target_markets", {}).get("state_level_searches", [])

                self.all_markets = []
                for market_data in state_markets:
                    market = MarketConfig(
                        state=market_data["state"],
                        priority=market_data["priority"],
                        weight=market_data["weight"],
                        search_radius_miles=market_data.get("search_radius_miles", 75),
                        expected_fmr_2br=market_data.get("expected_fmr_2br", 800),
                        expected_fmr_3br=market_data.get("expected_fmr_3br", 1000),
                        focus_areas=market_data.get("focus_areas", [])
                    )
                    self.all_markets.append(market)

                # Organize by priority for backward compatibility
                self.tier_1_markets = [m for m in self.all_markets if m.priority == "VERY_HIGH"]
                self.tier_2_markets = [m for m in self.all_markets if m.priority == "HIGH"]
                self.tier_3_markets = [m for m in self.all_markets if m.priority == "MEDIUM"]
                self.tier_4_markets = [m for m in self.all_markets if m.priority == "LOW"]

            else:
                # Fallback to default state-level markets if config.json not found
                self._load_default_state_markets()

        except Exception as e:
            print(f"Error loading market configs: {e}")
            # Fallback to default markets
            self._load_default_state_markets()

    def _load_default_state_markets(self):
        """Load default state-level market configurations"""
        self.all_markets = [
            MarketConfig("MI", "VERY_HIGH", 1.0, 75, 850, 1100, ["Detroit Metro", "Flint", "Lansing"]),
            MarketConfig("OH", "VERY_HIGH", 1.0, 75, 800, 1000, ["Cleveland", "Cincinnati", "Columbus"]),
            MarketConfig("PA", "VERY_HIGH", 1.0, 75, 850, 1100, ["Pittsburgh", "Philadelphia"]),
            MarketConfig("NY", "HIGH", 0.9, 75, 900, 1150, ["Buffalo", "Syracuse", "Rochester"]),
            MarketConfig("AL", "HIGH", 0.9, 75, 750, 950, ["Birmingham", "Mobile"]),
            MarketConfig("TN", "HIGH", 0.9, 75, 800, 1000, ["Memphis", "Nashville"]),
            MarketConfig("NC", "HIGH", 0.9, 75, 850, 1100, ["Charlotte", "Raleigh", "Wilmington"]),
        ]

        # Organize by priority for backward compatibility
        self.tier_1_markets = [m for m in self.all_markets if m.priority == "VERY_HIGH"]
        self.tier_2_markets = [m for m in self.all_markets if m.priority == "HIGH"]
        self.tier_3_markets = [m for m in self.all_markets if m.priority == "MEDIUM"]
        self.tier_4_markets = [m for m in self.all_markets if m.priority == "LOW"]
    
    def _load_investment_criteria(self):
        """Load investment criteria configurations"""
        
        # Primary criteria (based on Dustin's successful pattern)
        self.primary_criteria = InvestmentCriteria(
            min_price=50000,
            max_price=200000,
            min_bedrooms=2,
            max_bedrooms=4,
            min_bathrooms=1.0,
            max_bathrooms=3.0,
            min_square_feet=800,
            max_square_feet=2500,
            min_year_built=1970,
            property_types=["SINGLE_FAMILY", "MULTI_FAMILY", "TOWNHOUSE", "CONDO"],
            min_roi_percentage=8.0,
            target_roi_percentage=12.0
        )
        
        # Opportunity criteria (distressed properties)
        self.opportunity_criteria = InvestmentCriteria(
            min_price=25000,
            max_price=100000,
            min_bedrooms=2,
            max_bedrooms=5,
            min_bathrooms=1.0,
            max_bathrooms=4.0,
            min_square_feet=600,
            max_square_feet=3000,
            min_year_built=1950,
            property_types=["SINGLE_FAMILY", "MULTI_FAMILY", "TOWNHOUSE"],
            min_roi_percentage=15.0,
            target_roi_percentage=25.0
        )
        
        # Premium criteria (higher-end Section 8)
        self.premium_criteria = InvestmentCriteria(
            min_price=150000,
            max_price=350000,
            min_bedrooms=3,
            max_bedrooms=5,
            min_bathrooms=2.0,
            max_bathrooms=4.0,
            min_square_feet=1200,
            max_square_feet=3500,
            min_year_built=1980,
            property_types=["SINGLE_FAMILY", "TOWNHOUSE"],
            min_roi_percentage=6.0,
            target_roi_percentage=10.0
        )
    
    def _validate_config(self):
        """Validate configuration settings"""
        required_vars = [
            "REAL_ESTATE_API_KEY",
            "DATABASE_URL",
            "RESEND_API_KEY",
            "ALERT_EMAIL"
        ]
        
        missing_vars = []
        for var in required_vars:
            if not getattr(self, var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
    
    def get_tier_1_markets(self) -> List[MarketConfig]:
        """Get tier 1 markets"""
        return self.tier_1_markets
    
    def get_tier_2_markets(self) -> List[MarketConfig]:
        """Get tier 2 markets"""
        return self.tier_2_markets
    
    def get_all_markets(self) -> List[MarketConfig]:
        """Get all markets"""
        return self.all_markets
    
    def get_markets_by_priority(self, priority: str) -> List[MarketConfig]:
        """Get markets by priority level"""
        return [market for market in self.all_markets if market.priority == priority]
    
    def get_high_priority_markets(self) -> List[MarketConfig]:
        """Get high priority markets (VERY_HIGH and HIGH)"""
        return [
            market for market in self.all_markets 
            if market.priority in ["VERY_HIGH", "HIGH"]
        ]
    
    def get_investment_criteria(self, criteria_type: str = "primary") -> InvestmentCriteria:
        """Get investment criteria by type"""
        if criteria_type == "primary":
            return self.primary_criteria
        elif criteria_type == "opportunity":
            return self.opportunity_criteria
        elif criteria_type == "premium":
            return self.premium_criteria
        else:
            raise ValueError(f"Unknown criteria type: {criteria_type}")
    
    def get_api_headers(self, api_type: str) -> Dict[str, str]:
        """Get properly formatted API headers"""
        if api_type == "real_estate":
            return {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.REAL_ESTATE_API_KEY}"
            }
        elif api_type == "hud":
            return {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.HUD_API_KEY}"
            }
        elif api_type == "resend":
            return {
                "Authorization": f"Bearer {self.RESEND_API_KEY}",
                "Content-Type": "application/json"
            }
        else:
            raise ValueError(f"Unknown API type: {api_type}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            "app_name": self.APP_NAME,
            "search_interval_minutes": self.SEARCH_INTERVAL_MINUTES,
            "max_concurrent_searches": self.MAX_CONCURRENT_SEARCHES,
            "max_properties_per_search": self.MAX_PROPERTIES_PER_SEARCH,
            "total_markets": len(self.all_markets),
            "tier_1_markets": len(self.tier_1_markets),
            "tier_2_markets": len(self.tier_2_markets),
            "api_rate_limit_delay": self.API_RATE_LIMIT_DELAY,
        }
