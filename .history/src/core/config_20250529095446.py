#!/usr/bin/env python3
"""
Configuration Management for Section 8 Investor Pro
Handles all configuration settings, market definitions, and environment variables
"""

import os
import json
from typing import Dict, List, Any, Optional
from pathlib import Path
from dataclasses import dataclass
from dotenv import load_dotenv

@dataclass
class MarketConfig:
    """Configuration for a target market"""
    city: str
    state: str
    metro: str
    priority: str
    weight: float
    
@dataclass
class InvestmentCriteria:
    """Investment criteria configuration"""
    min_price: int
    max_price: int
    min_bedrooms: int
    max_bedrooms: int
    min_bathrooms: float
    max_bathrooms: float
    min_square_feet: int
    max_square_feet: int
    min_year_built: int
    property_types: List[str]
    min_roi_percentage: float
    target_roi_percentage: float

class Config:
    """Main configuration class"""
    
    def __init__(self):
        """Initialize configuration from environment and config files"""
        # Load environment variables
        load_dotenv()
        
        # API Configuration
        self.REAL_ESTATE_API_KEY = os.getenv("REAL_ESTATE_API_KEY")
        self.REAL_ESTATE_API_URL = os.getenv("REAL_ESTATE_API_URL", "https://api.realestateapi.com")
        self.HUD_API_KEY = os.getenv("HUD_API_KEY")
        self.HUD_API_URL = os.getenv("HUD_API_URL", "https://www.huduser.gov/hudapi/public")
        
        # Database Configuration
        self.DATABASE_URL = os.getenv("DATABASE_URL")
        self.DIRECT_URL = os.getenv("DIRECT_URL")
        
        # Email Configuration
        self.RESEND_API_KEY = os.getenv("RESEND_API_KEY")
        self.ALERT_EMAIL = os.getenv("ALERT_EMAIL")
        
        # Other APIs
        self.MAPBOX_ACCESS_TOKEN = os.getenv("MAPBOX_ACCESS_TOKEN")
        self.ZILLOW_API_KEY = os.getenv("ZILLOW_API_KEY")
        self.GOOGLE_MAPS_API_KEY = os.getenv("GOOGLE_MAPS_API_KEY")
        self.CENSUS_API_KEY = os.getenv("CENSUS_API_KEY")
        
        # App Configuration
        self.APP_URL = os.getenv("APP_URL", "http://localhost:3000")
        self.APP_NAME = os.getenv("APP_NAME", "Section8 Investor Pro")
        self.NODE_ENV = os.getenv("NODE_ENV", "development")
        
        # Performance Settings
        self.SEARCH_INTERVAL_MINUTES = int(os.getenv("SEARCH_INTERVAL_MINUTES", "5"))
        self.MAX_CONCURRENT_SEARCHES = int(os.getenv("MAX_CONCURRENT_SEARCHES", "10"))
        self.API_RATE_LIMIT_DELAY = int(os.getenv("API_RATE_LIMIT_DELAY", "2"))
        self.MAX_PROPERTIES_PER_SEARCH = int(os.getenv("MAX_PROPERTIES_PER_SEARCH", "100"))
        self.MAX_DAILY_API_CALLS = int(os.getenv("MAX_DAILY_API_CALLS", "10000"))
        
        # Rate Limiting
        self.RATE_LIMIT_WINDOW_MS = int(os.getenv("RATE_LIMIT_WINDOW_MS", "3600000"))
        self.RATE_LIMIT_MAX_REQUESTS = int(os.getenv("RATE_LIMIT_MAX_REQUESTS", "1000"))
        
        # Logging
        self.LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
        
        # Load market configurations
        self._load_market_configs()
        
        # Load investment criteria
        self._load_investment_criteria()
        
        # Validate configuration
        self._validate_config()
    
    def _load_market_configs(self):
        """Load market configurations"""
        # Tier 1: High-opportunity rust belt cities
        self.tier_1_markets = [
            MarketConfig("DETROIT", "MI", "Detroit-Warren-Dearborn", "VERY_HIGH", 1.0),
            MarketConfig("CLEVELAND", "OH", "Cleveland-Elyria", "VERY_HIGH", 1.0),
            MarketConfig("PITTSBURGH", "PA", "Pittsburgh", "VERY_HIGH", 1.0),
            MarketConfig("BUFFALO", "NY", "Buffalo-Cheektowaga-Niagara Falls", "VERY_HIGH", 1.0),
            MarketConfig("MILWAUKEE", "WI", "Milwaukee-Waukesha-West Allis", "VERY_HIGH", 1.0),
        ]
        
        # Tier 2: Secondary rust belt opportunities
        self.tier_2_markets = [
            MarketConfig("AKRON", "OH", "Akron", "HIGH", 0.9),
            MarketConfig("TOLEDO", "OH", "Toledo", "HIGH", 0.9),
            MarketConfig("YOUNGSTOWN", "OH", "Youngstown-Warren-Boardman", "HIGH", 0.9),
            MarketConfig("DAYTON", "OH", "Dayton", "HIGH", 0.9),
            MarketConfig("FLINT", "MI", "Flint", "HIGH", 0.9),
            MarketConfig("GARY", "IN", "Chicago-Naperville-Elgin", "HIGH", 0.9),
            MarketConfig("LANSING", "MI", "Lansing-East Lansing", "HIGH", 0.9),
            MarketConfig("GRAND RAPIDS", "MI", "Grand Rapids-Wyoming", "HIGH", 0.9),
        ]
        
        # Tier 3: Southern opportunities
        self.tier_3_markets = [
            MarketConfig("BIRMINGHAM", "AL", "Birmingham-Hoover", "HIGH", 0.8),
            MarketConfig("MEMPHIS", "TN", "Memphis", "HIGH", 0.8),
            MarketConfig("JACKSON", "MS", "Jackson", "HIGH", 0.8),
            MarketConfig("SHREVEPORT", "LA", "Shreveport-Bossier City", "HIGH", 0.8),
            MarketConfig("LITTLE ROCK", "AR", "Little Rock-North Little Rock-Conway", "HIGH", 0.8),
            MarketConfig("MOBILE", "AL", "Mobile", "MEDIUM", 0.7),
            MarketConfig("BATON ROUGE", "LA", "Baton Rouge", "MEDIUM", 0.7),
        ]
        
        # Tier 4: Emerging opportunities
        self.tier_4_markets = [
            MarketConfig("SYRACUSE", "NY", "Syracuse", "MEDIUM", 0.7),
            MarketConfig("ROCHESTER", "NY", "Rochester", "MEDIUM", 0.7),
            MarketConfig("ALBANY", "NY", "Albany-Schenectady-Troy", "MEDIUM", 0.7),
            MarketConfig("UTICA", "NY", "Utica-Rome", "MEDIUM", 0.6),
            MarketConfig("ERIE", "PA", "Erie", "MEDIUM", 0.6),
            MarketConfig("READING", "PA", "Reading", "MEDIUM", 0.6),
            MarketConfig("SCRANTON", "PA", "Scranton--Wilkes-Barre--Hazleton", "MEDIUM", 0.6),
        ]
        
        # Combine all markets
        self.all_markets = (
            self.tier_1_markets + 
            self.tier_2_markets + 
            self.tier_3_markets + 
            self.tier_4_markets
        )
    
    def _load_investment_criteria(self):
        """Load investment criteria configurations"""
        
        # Primary criteria (based on Dustin's successful pattern)
        self.primary_criteria = InvestmentCriteria(
            min_price=50000,
            max_price=200000,
            min_bedrooms=2,
            max_bedrooms=4,
            min_bathrooms=1.0,
            max_bathrooms=3.0,
            min_square_feet=800,
            max_square_feet=2500,
            min_year_built=1970,
            property_types=["SINGLE_FAMILY", "MULTI_FAMILY", "TOWNHOUSE", "CONDO"],
            min_roi_percentage=8.0,
            target_roi_percentage=12.0
        )
        
        # Opportunity criteria (distressed properties)
        self.opportunity_criteria = InvestmentCriteria(
            min_price=25000,
            max_price=100000,
            min_bedrooms=2,
            max_bedrooms=5,
            min_bathrooms=1.0,
            max_bathrooms=4.0,
            min_square_feet=600,
            max_square_feet=3000,
            min_year_built=1950,
            property_types=["SINGLE_FAMILY", "MULTI_FAMILY", "TOWNHOUSE"],
            min_roi_percentage=15.0,
            target_roi_percentage=25.0
        )
        
        # Premium criteria (higher-end Section 8)
        self.premium_criteria = InvestmentCriteria(
            min_price=150000,
            max_price=350000,
            min_bedrooms=3,
            max_bedrooms=5,
            min_bathrooms=2.0,
            max_bathrooms=4.0,
            min_square_feet=1200,
            max_square_feet=3500,
            min_year_built=1980,
            property_types=["SINGLE_FAMILY", "TOWNHOUSE"],
            min_roi_percentage=6.0,
            target_roi_percentage=10.0
        )
    
    def _validate_config(self):
        """Validate configuration settings"""
        required_vars = [
            "REAL_ESTATE_API_KEY",
            "DATABASE_URL",
            "RESEND_API_KEY",
            "ALERT_EMAIL"
        ]
        
        missing_vars = []
        for var in required_vars:
            if not getattr(self, var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
    
    def get_tier_1_markets(self) -> List[MarketConfig]:
        """Get tier 1 markets"""
        return self.tier_1_markets
    
    def get_tier_2_markets(self) -> List[MarketConfig]:
        """Get tier 2 markets"""
        return self.tier_2_markets
    
    def get_all_markets(self) -> List[MarketConfig]:
        """Get all markets"""
        return self.all_markets
    
    def get_markets_by_priority(self, priority: str) -> List[MarketConfig]:
        """Get markets by priority level"""
        return [market for market in self.all_markets if market.priority == priority]
    
    def get_high_priority_markets(self) -> List[MarketConfig]:
        """Get high priority markets (VERY_HIGH and HIGH)"""
        return [
            market for market in self.all_markets 
            if market.priority in ["VERY_HIGH", "HIGH"]
        ]
    
    def get_investment_criteria(self, criteria_type: str = "primary") -> InvestmentCriteria:
        """Get investment criteria by type"""
        if criteria_type == "primary":
            return self.primary_criteria
        elif criteria_type == "opportunity":
            return self.opportunity_criteria
        elif criteria_type == "premium":
            return self.premium_criteria
        else:
            raise ValueError(f"Unknown criteria type: {criteria_type}")
    
    def get_api_headers(self, api_type: str) -> Dict[str, str]:
        """Get properly formatted API headers"""
        if api_type == "real_estate":
            return {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.REAL_ESTATE_API_KEY}"
            }
        elif api_type == "hud":
            return {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.HUD_API_KEY}"
            }
        elif api_type == "resend":
            return {
                "Authorization": f"Bearer {self.RESEND_API_KEY}",
                "Content-Type": "application/json"
            }
        else:
            raise ValueError(f"Unknown API type: {api_type}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            "app_name": self.APP_NAME,
            "search_interval_minutes": self.SEARCH_INTERVAL_MINUTES,
            "max_concurrent_searches": self.MAX_CONCURRENT_SEARCHES,
            "max_properties_per_search": self.MAX_PROPERTIES_PER_SEARCH,
            "total_markets": len(self.all_markets),
            "tier_1_markets": len(self.tier_1_markets),
            "tier_2_markets": len(self.tier_2_markets),
            "api_rate_limit_delay": self.API_RATE_LIMIT_DELAY,
        }
